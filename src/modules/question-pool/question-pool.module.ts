import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QuestionPoolService } from './question-pool.service';
import { QuestionPoolConfigService } from './question-pool-config.service';
import { QuestionPool, QuestionPoolSchema } from '../mongodb/schemas/question-pool.schema';
import { QuestionGeneratorCronService } from './question-generator-cron.service';
import { PromptModule } from '../prompt/prompt.module';
import { BuildPromptModule } from '../build-prompt/build-prompt.module';
import { OptionType } from '../options/entities/option-type.entity';
import { OptionValue } from '../options/entities/option-value.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: QuestionPool.name, schema: QuestionPoolSchema },
    ]),
    TypeOrmModule.forFeature([
      OptionType,
      OptionValue,
    ]),
    forwardRef(() => PromptModule),
    forwardRef(() => BuildPromptModule),
  ],
  controllers: [],
  providers: [QuestionPoolService, QuestionPoolConfigService, QuestionGeneratorCronService],
  exports: [QuestionPoolService, QuestionPoolConfigService],
})
export class QuestionPoolModule {}
