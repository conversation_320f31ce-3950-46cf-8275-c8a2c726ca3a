import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { QuestionPoolService } from './question-pool.service';
import { QuestionPoolConfigService } from './question-pool-config.service';
import { ContentValidationService } from '../validation/content-validation.service';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { QuestionSelectionParams } from './interfaces/distribution.interface';

describe('QuestionPoolService - Distribution Features', () => {
  let service: QuestionPoolService;
  let mockQuestionPoolModel: any;
  let mockConfigService: any;
  let mockValidationService: any;

  beforeEach(async () => {
    // Mock the Mongoose model
    mockQuestionPoolModel = {
      aggregate: jest.fn().mockReturnValue({
        exec: jest.fn(),
      }),
      constructor: jest.fn(),
    };

    // Mock the config service
    mockConfigService = {
      getConfig: jest.fn().mockReturnValue({
        distribution: {
          defaultDifficultyDistribution: {
            Easy: 0.2,
            Medium: 0.6,
            Advanced: 0.2,
          },
          defaultQuestionTypeBalancing: {
            enabled: true,
            preferDiversity: true,
          },
          defaultDiversityConfig: {
            enabled: true,
            recencyPenaltyWeight: 0.3,
            frequencyPenaltyWeight: 0.2,
            recencyThresholdHours: 24,
          },
          defaultQualityValidationConfig: {
            enabled: true,
            failureHandlingStrategy: 'replace',
            maxReplacementAttempts: 3,
            minValidationSuccessRate: 0.8,
          },
          defaultFallbackConfig: {
            allowBestEffort: true,
            relaxDistributionOnShortfall: true,
            logFallbackReasons: true,
          },
        },
      }),
    };

    // Mock the validation service
    mockValidationService = {
      validateQuestions: jest.fn().mockResolvedValue({
        results: [],
        summary: {
          totalValidated: 0,
          passed: 0,
          failed: 0,
          successRate: 1,
          averageScore: 1,
        },
        processingTime: 0,
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionPoolService,
        {
          provide: getModelToken(QuestionPool.name),
          useValue: mockQuestionPoolModel,
        },
        {
          provide: QuestionPoolConfigService,
          useValue: mockConfigService,
        },
        {
          provide: ContentValidationService,
          useValue: mockValidationService,
        },
      ],
    }).compile();

    service = module.get<QuestionPoolService>(QuestionPoolService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getRandomQuestionsWithDistribution', () => {
    it('should perform simple random selection when distribution is skipped', async () => {
      const mockQuestions = [
        { _id: '1', type: 'multiple_choice', content: 'Test question 1', difficultyLevel: 'Easy' },
        { _id: '2', type: 'fill_blank', content: 'Test question 2', difficultyLevel: 'Medium' },
      ];

      mockQuestionPoolModel.aggregate().exec.mockResolvedValue(mockQuestions);
      mockQuestionPoolModel.constructor.mockImplementation((doc) => doc);

      const params: QuestionSelectionParams = {
        count: 2,
        skipDistribution: true,
        skipDiversity: true,
        skipValidation: true,
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(2);
      expect(result.metadata.totalRequested).toBe(2);
      expect(result.metadata.totalReturned).toBe(2);
      expect(result.metadata.fallbacksTriggered).toHaveLength(0);
    });

    it('should enforce difficulty distribution when enabled', async () => {
      // Mock different responses for different difficulty levels
      mockQuestionPoolModel.aggregate().exec
        .mockResolvedValueOnce([
          { _id: '1', type: 'multiple_choice', content: 'Easy question', difficultyLevel: 'Easy' },
          { _id: '2', type: 'multiple_choice', content: 'Easy question 2', difficultyLevel: 'Easy' },
        ])
        .mockResolvedValueOnce([
          { _id: '3', type: 'fill_blank', content: 'Medium question', difficultyLevel: 'Medium' },
          { _id: '4', type: 'fill_blank', content: 'Medium question 2', difficultyLevel: 'Medium' },
          { _id: '5', type: 'fill_blank', content: 'Medium question 3', difficultyLevel: 'Medium' },
          { _id: '6', type: 'fill_blank', content: 'Medium question 4', difficultyLevel: 'Medium' },
          { _id: '7', type: 'fill_blank', content: 'Medium question 5', difficultyLevel: 'Medium' },
          { _id: '8', type: 'fill_blank', content: 'Medium question 6', difficultyLevel: 'Medium' },
        ])
        .mockResolvedValueOnce([
          { _id: '9', type: 'open_ended', content: 'Advanced question', difficultyLevel: 'Advanced' },
          { _id: '10', type: 'open_ended', content: 'Advanced question 2', difficultyLevel: 'Advanced' },
        ]);

      mockQuestionPoolModel.constructor.mockImplementation((doc) => doc);

      const params: QuestionSelectionParams = {
        count: 10,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.2,    // 2 questions
            Medium: 0.6,  // 6 questions
            Advanced: 0.2, // 2 questions
          },
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(10);
      expect(result.metadata.distributionAchieved.Easy).toBe(0.2);
      expect(result.metadata.distributionAchieved.Medium).toBe(0.6);
      expect(result.metadata.distributionAchieved.Advanced).toBe(0.2);
    });

    it('should handle shortfall gracefully with fallback strategies', async () => {
      // Mock insufficient questions for one difficulty level
      mockQuestionPoolModel.aggregate().exec
        .mockResolvedValueOnce([
          { _id: '1', type: 'multiple_choice', content: 'Easy question', difficultyLevel: 'Easy' },
        ]) // Only 1 Easy question instead of 2
        .mockResolvedValueOnce([
          { _id: '2', type: 'fill_blank', content: 'Medium question', difficultyLevel: 'Medium' },
          { _id: '3', type: 'fill_blank', content: 'Medium question 2', difficultyLevel: 'Medium' },
          { _id: '4', type: 'fill_blank', content: 'Medium question 3', difficultyLevel: 'Medium' },
          { _id: '5', type: 'fill_blank', content: 'Medium question 4', difficultyLevel: 'Medium' },
          { _id: '6', type: 'fill_blank', content: 'Medium question 5', difficultyLevel: 'Medium' },
          { _id: '7', type: 'fill_blank', content: 'Medium question 6', difficultyLevel: 'Medium' },
        ])
        .mockResolvedValueOnce([
          { _id: '8', type: 'open_ended', content: 'Advanced question', difficultyLevel: 'Advanced' },
          { _id: '9', type: 'open_ended', content: 'Advanced question 2', difficultyLevel: 'Advanced' },
        ])
        .mockResolvedValueOnce([
          { _id: '10', type: 'multiple_choice', content: 'Additional question', difficultyLevel: 'Medium' },
        ]); // Additional question to fill the gap

      mockQuestionPoolModel.constructor.mockImplementation((doc) => doc);

      const params: QuestionSelectionParams = {
        count: 10,
        distributionConfig: {
          difficultyDistribution: {
            Easy: 0.2,    // 2 questions requested
            Medium: 0.6,  // 6 questions
            Advanced: 0.2, // 2 questions
          },
          fallback: {
            allowBestEffort: true,
            relaxDistributionOnShortfall: true,
            logFallbackReasons: true,
          },
        },
      };

      const result = await service.getRandomQuestionsWithDistribution(params);

      expect(result.questions).toHaveLength(10);
      expect(result.metadata.fallbacksTriggered.length).toBeGreaterThan(0);
      expect(result.metadata.fallbacksTriggered.some(reason => reason.includes('Insufficient Easy questions'))).toBe(true);
    });
  });

  describe('calculateDifficultyTargetCounts', () => {
    it('should calculate correct target counts for difficulty distribution', () => {
      const distribution = { Easy: 0.2, Medium: 0.6, Advanced: 0.2 };
      const result = (service as any).calculateDifficultyTargetCounts(10, distribution);

      expect(result.Easy).toBe(2);
      expect(result.Medium).toBe(6);
      expect(result.Advanced).toBe(2);
    });
  });
});
