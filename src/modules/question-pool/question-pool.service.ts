import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { QuestionPool } from '../mongodb/schemas/question-pool.schema';
import { ExerciseQuestionItem } from '../prompt/interfaces/exercise-result.interface';

@Injectable()
export class QuestionPoolService {
  private readonly logger = new Logger(QuestionPoolService.name);

  constructor(
    @InjectModel(QuestionPool.name)
    private questionPoolModel: Model<QuestionPool>,
  ) {}

  /**
   * Add a question to the pool
   * @param question The question to add
   * @param optionValue Optional reference to option values used
   * @returns The saved question
   */
  async addQuestion(
    question: ExerciseQuestionItem,
    optionValue?: Record<string, any>,
  ): Promise<QuestionPool> {
    try {
      const newQuestion = new this.questionPoolModel({
        type: question.type,
        content: question.content,
        options: question.options,
        answer: question.answer,
        explain: question.explain,
        imagePrompt: question.imagePrompt,
        image: question.image,
        subject: question.subject, // Parent subject
        parentSubject: question.parentSubject, // Subject type parent
        childSubject: question.childSubject, // Subject type child
        grade: question.grade, // Grade level
        language: question.language || 'English', // Language (default to English)
        optionValue: optionValue,
        status: 'active',
      });

      return await newQuestion.save();
    } catch (error) {
      this.logger.error(`Error adding question to pool: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Add multiple questions to the pool
   * @param questions The questions to add
   * @param optionValue Optional reference to option values used
   * @returns The saved questions
   */
  async addQuestions(
    questions: ExerciseQuestionItem[],
    optionValue?: Record<string, any>,
  ): Promise<QuestionPool[]> {
    try {
      const savedQuestions: QuestionPool[] = [];

      for (const question of questions) {
        const savedQuestion = await this.addQuestion(question, optionValue);
        savedQuestions.push(savedQuestion);
      }

      return savedQuestions;
    } catch (error) {
      this.logger.error(`Error adding questions to pool: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get questions from the pool with filtering
   * @param filters Filters to apply
   * @param limit Maximum number of questions to return
   * @param skip Number of questions to skip (for pagination)
   * @returns The filtered questions
   */
  async getQuestions(
    filters: {
      subject?: string;
      parentSubject?: string;
      childSubject?: string;
      type?: string;
      optionValueId?: string;
      status?: string;
      grade?: string;
      language?: string;
    },
    limit: number = 10,
    skip: number = 0,
  ): Promise<{ questions: QuestionPool[]; total: number }> {
    try {
      const query: any = { status: 'active' };

      if (filters.subject) {
        query.subject = filters.subject;
      }

      if (filters.parentSubject) {
        query.parentSubject = filters.parentSubject;
      }

      if (filters.childSubject) {
        query.childSubject = filters.childSubject;
      }

      if (filters.type) {
        query.type = filters.type;
      }

      if (filters.optionValueId) {
        query['optionValue.id'] = filters.optionValueId;
      }

      if (filters.status) {
        query.status = filters.status;
      }

      if (filters.grade) {
        query.grade = filters.grade;
      }

      if (filters.language) {
        query.language = filters.language;
      }

      const [questions, total] = await Promise.all([
        this.questionPoolModel
          .find(query)
          .sort({ createdAt: -1 })
          .limit(limit)
          .skip(skip)
          .exec(),
        this.questionPoolModel.countDocuments(query).exec(),
      ]);

      return { questions, total };
    } catch (error) {
      this.logger.error(`Error getting questions from pool: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update a question's status
   * @param id The question ID
   * @param status The new status
   * @returns The updated question
   */
  async updateQuestionStatus(
    id: string,
    status: 'active' | 'inactive',
  ): Promise<QuestionPool | null> {
    try {
      return await this.questionPoolModel.findByIdAndUpdate(
        id,
        { status },
        { new: true },
      );
    } catch (error) {
      this.logger.error(`Error updating question status: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get random questions from the pool with filtering
   * @param filters Filters to apply
   * @param count Number of random questions to return
   * @returns The randomly selected questions
   */
  async getRandomQuestions(
    filters: {
      subject?: string;
      parentSubject?: string;
      childSubject?: string;
      type?: string | string[];
      difficulty?: string;
      status?: string;
      grade?: string;
      language?: string;
    },
    count: number = 10,
  ): Promise<QuestionPool[]> {
    try {
      const query: any = { status: filters.status || 'active' };

      if (filters.subject) {
        query.subject = filters.subject;
      }

      if (filters.parentSubject) {
        query.parentSubject = filters.parentSubject;
      }

      if (filters.childSubject) {
        query.childSubject = filters.childSubject;
      }

      if (filters.type) {
        if (Array.isArray(filters.type)) {
          query.type = { $in: filters.type };
        } else {
          query.type = filters.type;
        }
      }

      if (filters.difficulty) {
        query['optionValue.difficulty'] = filters.difficulty;
      }

      if (filters.grade) {
        query.grade = filters.grade;
      }

      if (filters.language) {
        query.language = filters.language;
      }

      // Use aggregation pipeline with $sample to get random documents
      const randomQuestions = await this.questionPoolModel.aggregate([
        { $match: query },
        { $sample: { size: count } }
      ]).exec();

      // Convert the plain objects back to Mongoose documents
      const questions = randomQuestions.map(doc => {
        return new this.questionPoolModel(doc);
      });

      return questions;
    } catch (error) {
      this.logger.error(`Error getting random questions from pool: ${error.message}`, error.stack);
      throw error;
    }
  }
}
