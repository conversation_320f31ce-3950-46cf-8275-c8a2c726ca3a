import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import {
  WorksheetDocument,
  WorksheetDocumentSchema,
} from './schemas/worksheet-document.schema';
import {
  WorksheetPromptResult,
  WorksheetPromptResultSchema,
} from './schemas/worksheet-prompt-result.schema';
import {
  QueryCache,
  QueryCacheSchema,
} from './schemas/query-cache.schema';
import {
  EmbeddingCache,
  EmbeddingCacheSchema,
} from './schemas/embedding-cache.schema';
import {
  QuestionPool,
  QuestionPoolSchema,
} from './schemas/question-pool.schema';

@Module({
  imports: [
    MongooseModule.forRootAsync({
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
        useNewUrlParser: true,
        useUnifiedTopology: true,
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: WorksheetDocument.name, schema: WorksheetDocumentSchema },
      { name: WorksheetPromptResult.name, schema: WorksheetPromptResultSchema },
      { name: QueryCache.name, schema: QueryCacheSchema },
      { name: EmbeddingCache.name, schema: EmbeddingCacheSchema },
      { name: QuestionPool.name, schema: QuestionPoolSchema },
    ]),
  ],
  exports: [MongooseModule],
})
export class MongodbModule {}
