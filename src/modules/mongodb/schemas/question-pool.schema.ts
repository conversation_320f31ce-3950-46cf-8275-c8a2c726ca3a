import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class QuestionPool extends Document {
  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  content: string;

  @Prop({ type: [String] })
  options?: string[];

  @Prop({ type: [String] })
  answer?: string[];

  @Prop()
  explain?: string;

  @Prop()
  imagePrompt?: string;

  @Prop()
  image?: string;

  @Prop()
  subject?: string; // Parent subject category

  @Prop()
  parentSubject?: string; // Subject type parent

  @Prop()
  childSubject?: string; // Subject type child

  @Prop({ type: Object })
  optionValue?: Record<string, any>; // Reference to option values used

  @Prop()
  grade?: string; // Grade level for the question

  @Prop({ default: 'English' })
  language?: string; // Language of the question

  @Prop({ default: 'active', enum: ['active', 'inactive'] })
  status: string;

  @Prop({ enum: ['Easy', 'Medium', 'Advanced'] })
  difficultyLevel?: string; // Direct difficulty level field for efficient querying

  @Prop({ type: Date })
  lastSelectedTimestamp?: Date; // For diversity tracking

  @Prop({ type: Number, default: 0 })
  selectionFrequency?: number; // For diversity tracking

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const QuestionPoolSchema = SchemaFactory.createForClass(QuestionPool);

// Add indexes for efficient filtering
QuestionPoolSchema.index({ subject: 1 });
QuestionPoolSchema.index({ parentSubject: 1 });
QuestionPoolSchema.index({ childSubject: 1 });
QuestionPoolSchema.index({ status: 1 });
QuestionPoolSchema.index({ type: 1 });
QuestionPoolSchema.index({ 'optionValue.id': 1 });
QuestionPoolSchema.index({ grade: 1 });
QuestionPoolSchema.index({ language: 1 });
QuestionPoolSchema.index({ difficultyLevel: 1 });
QuestionPoolSchema.index({ lastSelectedTimestamp: 1 });
QuestionPoolSchema.index({ selectionFrequency: 1 });
// Compound indexes for efficient distribution queries
QuestionPoolSchema.index({ status: 1, difficultyLevel: 1, type: 1 });
QuestionPoolSchema.index({ subject: 1, difficultyLevel: 1, type: 1 });
