import { registerAs } from '@nestjs/config';
import { configService } from '../env/env.config';

export interface QuestionPoolConfigType {
  enabled: boolean;
  defaultSelectionStrategy: 'pool-only' | 'ai-only' | 'hybrid' | 'mixed';
  minPoolQuestionsThreshold: number;
  featureFlags: {
    allowPoolOnlyStrategy: boolean;
    allowAiOnlyStrategy: boolean;
    allowHybridStrategy: boolean;
    allowMixedStrategy: boolean;
  };
}

export default registerAs('questionPool', (): QuestionPoolConfigType => {
  return {
    enabled: configService.get('QUESTION_POOL_ENABLED') === 'true' || 
             configService.get('QUESTION_POOL_ENABLED') === undefined, // Default to true
    defaultSelectionStrategy: (configService.get('DEFAULT_SELECTION_STRATEGY') as 'pool-only' | 'ai-only' | 'hybrid' | 'mixed') || 'hybrid',
    minPoolQuestionsThreshold: parseInt(
      configService.get('MIN_POOL_QUESTIONS_THRESHOLD') ?? '10',
      10,
    ),
    featureFlags: {
      allowPoolOnlyStrategy: configService.get('ALLOW_POOL_ONLY_STRATEGY') !== 'false', // Default to true
      allowAiOnlyStrategy: configService.get('ALLOW_AI_ONLY_STRATEGY') !== 'false', // Default to true
      allowHybridStrategy: configService.get('ALLOW_HYBRID_STRATEGY') !== 'false', // Default to true
      allowMixedStrategy: configService.get('ALLOW_MIXED_STRATEGY') !== 'false', // Default to true
    },
  };
});
